# Flink 裁判文书数据清洗任务

这是一个基于Apache Flink的实时数据清洗任务，专门用于处理从Kafka接收的裁判文书数据。

## 项目结构

```
flink-risk-demo/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/example/flink/
│   │   │       ├── job/
│   │   │       │   └── JudgmentDocumentCleanJob.java    # 主任务类
│   │   │       ├── model/
│   │   │       │   └── JudgmentDocument.java            # 数据模型
│   │   │       ├── step/
│   │   │       │   └── CleanStep.java                   # 数据清洗逻辑
│   │   │       └── config/
│   │   │           └── KafkaSourceConfig.java           # Kafka配置
│   │   └── resources/
│   │       └── application.properties                   # 配置文件
│   └── test/
│       └── java/
│           └── com/example/flink/step/
│               └── CleanStepTest.java                   # 测试类
├── pom.xml                                              # Maven配置
└── README.md                                            # 项目说明
```

## 功能特性

### 数据清洗功能
- **HTML标签清理**：移除文档内容中的HTML标签
- **空白字符标准化**：统一处理空格、换行符、制表符等
- **特殊字符移除**：清除控制字符和不可见字符
- **个人信息脱敏**：自动识别并脱敏手机号码、身份证号等敏感信息
- **数据验证**：检查必填字段、内容长度等数据质量指标

### 技术特性
- **实时处理**：基于Flink流处理引擎，支持实时数据清洗
- **容错机制**：支持检查点和重启策略，保证任务稳定性
- **监控支持**：区分成功和失败的数据，便于监控和调试
- **配置灵活**：通过配置文件控制清洗规则和参数

## 配置说明

### Kafka配置
```properties
# Kafka服务器地址
kafka.bootstrap.servers=localhost:9092
# 消费者组ID
kafka.group.id=judgment-document-clean-group
# 输入主题（原始数据）
kafka.topic.input=judgment-documents-raw
# 输出主题（清洗后数据）
kafka.topic.output=judgment-documents-cleaned
```

### 清洗规则配置
```properties
# 是否移除HTML标签
clean.remove.html.tags=true
# 是否标准化空白字符
clean.normalize.whitespace=true
# 是否移除特殊字符
clean.remove.special.chars=true
# 最小内容长度
clean.min.content.length=100
# 最大内容长度
clean.max.content.length=50000
```

## 数据格式

### 输入数据格式（JSON）
```json
{
  "id": "doc_001",
  "case_number": "(2023)京01民初123号",
  "court_name": "北京市第一中级人民法院",
  "case_type": "民事案件",
  "judgment_date": "2023-12-01",
  "parties": "原告：张三 被告：李四",
  "content": "裁判文书正文内容...",
  "judge_name": "王法官",
  "case_category": "合同纠纷",
  "verdict": "判决结果...",
  "source": "court_system"
}
```

### 输出数据格式（JSON）
```json
{
  "id": "doc_001",
  "case_number": "(2023)京01民初123号",
  "court_name": "北京市第一中级人民法院",
  "case_type": "民事案件",
  "judgment_date": "2023-12-01",
  "parties": "原告：张三 被告：李四",
  "content": "清洗后的裁判文书正文内容...",
  "judge_name": "王法官",
  "case_category": "合同纠纷",
  "verdict": "清洗后的判决结果...",
  "source": "court_system",
  "is_cleaned": true,
  "clean_errors": null,
  "timestamp": 1703145600000
}
```

## 构建和运行

### 1. 编译项目
```bash
mvn clean compile
```

### 2. 运行测试
```bash
mvn test
```

### 3. 打包项目
```bash
mvn clean package
```

### 4. 提交到Flink集群
```bash
flink run -c com.example.flink.job.JudgmentDocumentCleanJob target/flink-risk-demo-1.0-SNAPSHOT.jar
```

### 5. 本地运行（开发调试）
```bash
mvn exec:java -Dexec.mainClass="com.example.flink.job.JudgmentDocumentCleanJob"
```

## 监控和调试

任务运行时会输出以下信息：
- **SUCCESS**：成功清洗的文档信息
- **ERROR**：清洗失败的文档信息和错误原因

可以通过Flink Web UI监控任务状态：
- 访问 http://localhost:8081
- 查看任务运行状态、吞吐量、延迟等指标

## 扩展说明

### 添加新的清洗规则
在 `CleanStep.java` 中的 `map` 方法中添加新的清洗逻辑。

### 修改数据模型
在 `JudgmentDocument.java` 中添加新的字段，并更新相应的清洗逻辑。

### 调整配置参数
修改 `application.properties` 文件中的配置项，重新部署任务即可生效。

## 依赖版本

- Apache Flink: 1.17.1
- Apache Kafka: 3.4.0
- Jackson: 2.15.2
- Java: 8+
