package com.example.flink.step;

import com.example.flink.model.JudgmentDocument;
import org.junit.Before;
import org.junit.Test;

import java.util.Properties;

import static org.junit.Assert.*;

/**
 * CleanStep测试类
 */
public class CleanStepTest {
    
    private CleanStep cleanStep;
    private Properties properties;
    
    @Before
    public void setUp() throws Exception {
        properties = new Properties();
        properties.setProperty("clean.remove.html.tags", "true");
        properties.setProperty("clean.normalize.whitespace", "true");
        properties.setProperty("clean.remove.special.chars", "true");
        properties.setProperty("clean.min.content.length", "10");
        properties.setProperty("clean.max.content.length", "1000");
        
        cleanStep = new CleanStep(properties);
        cleanStep.open(null);
    }
    
    @Test
    public void testCleanHtmlTags() throws Exception {
        JudgmentDocument document = new JudgmentDocument();
        document.setId("test-1");
        document.setContent("<p>这是一个<b>测试</b>文档</p>");
        document.setCaseNumber("(2023)京01民初123号");
        document.setCourtName("北京市第一中级人民法院");
        
        JudgmentDocument result = cleanStep.map(document);
        
        assertNotNull(result);
        assertTrue(result.getIsCleaned());
        assertEquals("这是一个测试文档", result.getContent());
    }
    
    @Test
    public void testNormalizeWhitespace() throws Exception {
        JudgmentDocument document = new JudgmentDocument();
        document.setId("test-2");
        document.setContent("这是一个   测试\n\n文档\t内容");
        document.setCaseNumber("(2023)京01民初124号");
        document.setCourtName("北京市第一中级人民法院");
        
        JudgmentDocument result = cleanStep.map(document);
        
        assertNotNull(result);
        assertTrue(result.getIsCleaned());
        assertEquals("这是一个 测试 文档 内容", result.getContent());
    }
    
    @Test
    public void testValidationErrors() throws Exception {
        JudgmentDocument document = new JudgmentDocument();
        document.setId("test-3");
        document.setContent("短"); // 内容太短
        // 缺少必填字段
        
        JudgmentDocument result = cleanStep.map(document);
        
        assertNotNull(result);
        assertTrue(result.getIsCleaned());
        assertNotNull(result.getCleanErrors());
        assertTrue(result.getCleanErrors().contains("Content too short"));
        assertTrue(result.getCleanErrors().contains("Missing case number"));
        assertTrue(result.getCleanErrors().contains("Missing court name"));
    }
    
    @Test
    public void testPersonalInfoMasking() throws Exception {
        JudgmentDocument document = new JudgmentDocument();
        document.setId("test-4");
        document.setContent("联系电话：13812345678，身份证号：110101199001011234");
        document.setCaseNumber("(2023)京01民初125号");
        document.setCourtName("北京市第一中级人民法院");
        
        JudgmentDocument result = cleanStep.map(document);
        
        assertNotNull(result);
        assertTrue(result.getIsCleaned());
        assertFalse(result.getContent().contains("13812345678"));
        assertFalse(result.getContent().contains("110101199001011234"));
    }
    
    @Test
    public void testValidDocument() throws Exception {
        JudgmentDocument document = new JudgmentDocument();
        document.setId("test-5");
        document.setContent("这是一个完整的裁判文书内容，包含了足够的信息用于测试数据清洗功能。");
        document.setCaseNumber("(2023)京01民初126号");
        document.setCourtName("北京市第一中级人民法院");
        document.setCaseType("民事案件");
        document.setJudgmentDate("2023-12-01");
        
        JudgmentDocument result = cleanStep.map(document);
        
        assertNotNull(result);
        assertTrue(result.getIsCleaned());
        assertNull(result.getCleanErrors());
        assertEquals("test-5", result.getId());
        assertEquals("(2023)京01民初126号", result.getCaseNumber());
        assertEquals("北京市第一中级人民法院", result.getCourtName());
    }
}
