package com.example.flink.step;

import com.example.flink.model.JudgmentDocument;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;
import java.util.regex.Pattern;

/**
 * 裁判文书数据清洗步骤
 * 主要的数据清洗逻辑都在这个类中实现
 */
public class CleanStep extends RichMapFunction<JudgmentDocument, JudgmentDocument> {
    
    private static final Logger LOG = LoggerFactory.getLogger(CleanStep.class);
    
    // 配置参数
    private boolean removeHtmlTags;
    private boolean normalizeWhitespace;
    private boolean removeSpecialChars;
    private int minContentLength;
    private int maxContentLength;
    
    // 正则表达式模式
    private transient Pattern htmlTagPattern;
    private transient Pattern whitespacePattern;
    private transient Pattern specialCharPattern;
    private transient Pattern phonePattern;
    private transient Pattern idCardPattern;
    
    private final Properties properties;
    
    public CleanStep(Properties properties) {
        this.properties = properties;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化配置
        this.removeHtmlTags = Boolean.parseBoolean(
            properties.getProperty("clean.remove.html.tags", "true"));
        this.normalizeWhitespace = Boolean.parseBoolean(
            properties.getProperty("clean.normalize.whitespace", "true"));
        this.removeSpecialChars = Boolean.parseBoolean(
            properties.getProperty("clean.remove.special.chars", "true"));
        this.minContentLength = Integer.parseInt(
            properties.getProperty("clean.min.content.length", "100"));
        this.maxContentLength = Integer.parseInt(
            properties.getProperty("clean.max.content.length", "50000"));
        
        // 初始化正则表达式模式
        this.htmlTagPattern = Pattern.compile("<[^>]+>");
        this.whitespacePattern = Pattern.compile("\\s+");
        this.specialCharPattern = Pattern.compile("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]");
        this.phonePattern = Pattern.compile("1[3-9]\\d{9}");
        this.idCardPattern = Pattern.compile("\\d{17}[\\dXx]");
        
        LOG.info("CleanStep initialized with config: removeHtmlTags={}, normalizeWhitespace={}, " +
                "removeSpecialChars={}, minContentLength={}, maxContentLength={}", 
                removeHtmlTags, normalizeWhitespace, removeSpecialChars, minContentLength, maxContentLength);
    }
    
    @Override
    public JudgmentDocument map(JudgmentDocument document) throws Exception {
        try {
            LOG.debug("Processing document: {}", document.getId());
            
            // 如果文档已经有反序列化错误，直接返回
            if (document.getCleanErrors() != null && document.getCleanErrors().contains("Deserialization failed")) {
                return document;
            }
            
            // 执行数据清洗
            document = cleanContent(document);
            document = cleanPersonalInfo(document);
            document = validateDocument(document);
            
            // 标记为已清洗
            document.setIsCleaned(true);
            
            LOG.debug("Successfully cleaned document: {}", document.getId());
            return document;
            
        } catch (Exception e) {
            LOG.error("Error cleaning document: {}", document.getId(), e);
            document.setCleanErrors("Cleaning failed: " + e.getMessage());
            document.setIsCleaned(false);
            return document;
        }
    }
    
    /**
     * 清洗文档内容
     */
    private JudgmentDocument cleanContent(JudgmentDocument document) {
        String content = document.getContent();
        if (content == null || content.trim().isEmpty()) {
            return document;
        }
        
        // 移除HTML标签
        if (removeHtmlTags) {
            content = htmlTagPattern.matcher(content).replaceAll("");
        }
        
        // 移除特殊控制字符
        if (removeSpecialChars) {
            content = specialCharPattern.matcher(content).replaceAll("");
        }
        
        // 标准化空白字符
        if (normalizeWhitespace) {
            content = whitespacePattern.matcher(content).replaceAll(" ");
            content = content.trim();
        }
        
        document.setContent(content);
        
        // 清洗其他字段
        document.setCourtName(cleanTextField(document.getCourtName()));
        document.setCaseNumber(cleanTextField(document.getCaseNumber()));
        document.setParties(cleanTextField(document.getParties()));
        document.setJudgeName(cleanTextField(document.getJudgeName()));
        document.setVerdict(cleanTextField(document.getVerdict()));
        
        return document;
    }
    
    /**
     * 清洗个人敏感信息
     */
    private JudgmentDocument cleanPersonalInfo(JudgmentDocument document) {
        String content = document.getContent();
        if (content == null) {
            return document;
        }
        
        // 脱敏手机号码
        content = phonePattern.matcher(content).replaceAll("1****$1");
        
        // 脱敏身份证号码
        content = idCardPattern.matcher(content).replaceAll("****$1");
        
        document.setContent(content);
        return document;
    }
    
    /**
     * 验证文档数据质量
     */
    private JudgmentDocument validateDocument(JudgmentDocument document) {
        StringBuilder errors = new StringBuilder();
        
        // 验证必填字段
        if (isNullOrEmpty(document.getId())) {
            errors.append("Missing document ID; ");
        }
        
        if (isNullOrEmpty(document.getContent())) {
            errors.append("Missing content; ");
        } else {
            // 验证内容长度
            int contentLength = document.getContent().length();
            if (contentLength < minContentLength) {
                errors.append("Content too short (").append(contentLength).append(" < ").append(minContentLength).append("); ");
            }
            if (contentLength > maxContentLength) {
                errors.append("Content too long (").append(contentLength).append(" > ").append(maxContentLength).append("); ");
                // 截断过长的内容
                document.setContent(document.getContent().substring(0, maxContentLength));
            }
        }
        
        if (isNullOrEmpty(document.getCaseNumber())) {
            errors.append("Missing case number; ");
        }
        
        if (isNullOrEmpty(document.getCourtName())) {
            errors.append("Missing court name; ");
        }
        
        // 设置验证错误信息
        if (errors.length() > 0) {
            String existingErrors = document.getCleanErrors();
            String newErrors = errors.toString();
            document.setCleanErrors(existingErrors == null ? newErrors : existingErrors + newErrors);
        }
        
        return document;
    }
    
    /**
     * 清洗文本字段
     */
    private String cleanTextField(String text) {
        if (text == null) {
            return null;
        }
        
        String cleaned = text.trim();
        
        if (removeHtmlTags) {
            cleaned = htmlTagPattern.matcher(cleaned).replaceAll("");
        }
        
        if (removeSpecialChars) {
            cleaned = specialCharPattern.matcher(cleaned).replaceAll("");
        }
        
        if (normalizeWhitespace) {
            cleaned = whitespacePattern.matcher(cleaned).replaceAll(" ");
            cleaned = cleaned.trim();
        }
        
        return cleaned.isEmpty() ? null : cleaned;
    }
    
    /**
     * 检查字符串是否为空
     */
    private boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
}
