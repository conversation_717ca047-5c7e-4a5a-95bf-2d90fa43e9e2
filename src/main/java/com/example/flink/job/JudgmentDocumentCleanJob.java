package com.example.flink.job;

import com.example.flink.config.KafkaSourceConfig;
import com.example.flink.model.JudgmentDocument;
import com.example.flink.step.CleanStep;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.PrintSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.time.Duration;
import java.util.Properties;

/**
 * 裁判文书数据清洗Flink任务
 * 主要功能：
 * 1. 从Kafka读取原始裁判文书数据
 * 2. 执行数据清洗逻辑
 * 3. 将清洗后的数据写入Kafka
 */
public class JudgmentDocumentCleanJob {
    
    private static final Logger LOG = LoggerFactory.getLogger(JudgmentDocumentCleanJob.class);
    
    public static void main(String[] args) throws Exception {
        // 加载配置
        Properties properties = loadProperties();
        
        // 创建Flink执行环境
        StreamExecutionEnvironment env = createExecutionEnvironment(properties);
        
        // 创建Kafka数据源
        KafkaSource<JudgmentDocument> kafkaSource = KafkaSourceConfig.createKafkaSource(properties);
        
        // 创建数据流
        DataStream<JudgmentDocument> sourceStream = env
                .fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "Kafka Source")
                .name("Judgment Document Source");
        
        // 执行数据清洗
        DataStream<JudgmentDocument> cleanedStream = sourceStream
                .map(new CleanStep(properties))
                .name("Clean Step");
        
        // 过滤出成功清洗的数据
        DataStream<JudgmentDocument> validStream = cleanedStream
                .filter(new ValidDocumentFilter())
                .name("Valid Document Filter");
        
        // 过滤出清洗失败的数据用于监控
        DataStream<JudgmentDocument> errorStream = cleanedStream
                .filter(new ErrorDocumentFilter())
                .name("Error Document Filter");
        
        // 输出清洗后的数据到Kafka
        KafkaSink<JudgmentDocument> kafkaSink = KafkaSourceConfig.createKafkaSink(properties);
        validStream.sinkTo(kafkaSink).name("Kafka Sink");
        
        // 输出错误数据到控制台用于调试
        errorStream.addSink(new PrintSinkFunction<>("ERROR", false)).name("Error Print Sink");
        
        // 添加监控输出
        validStream.addSink(new PrintSinkFunction<>("SUCCESS", false)).name("Success Print Sink");
        
        LOG.info("Starting Judgment Document Clean Job...");
        
        // 执行任务
        env.execute("Judgment Document Clean Job");
    }
    
    /**
     * 创建Flink执行环境
     */
    private static StreamExecutionEnvironment createExecutionEnvironment(Properties properties) {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        int parallelism = Integer.parseInt(properties.getProperty("flink.parallelism", "4"));
        env.setParallelism(parallelism);
        
        // 启用检查点
        long checkpointInterval = Long.parseLong(properties.getProperty("flink.checkpoint.interval", "60000"));
        env.enableCheckpointing(checkpointInterval);
        
        // 设置检查点超时时间
        long checkpointTimeout = Long.parseLong(properties.getProperty("flink.checkpoint.timeout", "300000"));
        env.getCheckpointConfig().setCheckpointTimeout(checkpointTimeout);
        
        // 设置重启策略
        String restartStrategy = properties.getProperty("flink.restart.strategy", "fixed-delay");
        if ("fixed-delay".equals(restartStrategy)) {
            int restartAttempts = Integer.parseInt(properties.getProperty("flink.restart.attempts", "3"));
            long restartDelay = Long.parseLong(properties.getProperty("flink.restart.delay", "10000"));
            env.setRestartStrategy(org.apache.flink.api.common.restartstrategy.RestartStrategies
                    .fixedDelayRestart(restartAttempts, Time.fromDuration(Duration.ofMillis(restartDelay))));
        }
        
        LOG.info("Flink environment configured: parallelism={}, checkpointInterval={}ms, checkpointTimeout={}ms", 
                parallelism, checkpointInterval, checkpointTimeout);
        
        return env;
    }
    
    /**
     * 加载配置文件
     */
    private static Properties loadProperties() {
        Properties properties = new Properties();
        try (InputStream input = JudgmentDocumentCleanJob.class
                .getClassLoader().getResourceAsStream("application.properties")) {
            if (input != null) {
                properties.load(input);
                LOG.info("Loaded properties from application.properties");
            } else {
                LOG.warn("application.properties not found, using default values");
            }
        } catch (Exception e) {
            LOG.error("Failed to load properties", e);
        }
        return properties;
    }
    
    /**
     * 有效文档过滤器
     */
    public static class ValidDocumentFilter implements FilterFunction<JudgmentDocument> {
        @Override
        public boolean filter(JudgmentDocument document) throws Exception {
            return document.getIsCleaned() != null && 
                   document.getIsCleaned() && 
                   (document.getCleanErrors() == null || document.getCleanErrors().trim().isEmpty());
        }
    }
    
    /**
     * 错误文档过滤器
     */
    public static class ErrorDocumentFilter implements FilterFunction<JudgmentDocument> {
        @Override
        public boolean filter(JudgmentDocument document) throws Exception {
            return document.getIsCleaned() == null || 
                   !document.getIsCleaned() || 
                   (document.getCleanErrors() != null && !document.getCleanErrors().trim().isEmpty());
        }
    }
}
