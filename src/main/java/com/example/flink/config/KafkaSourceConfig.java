package com.example.flink.config;

import com.example.flink.model.JudgmentDocument;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * Kafka数据源配置类
 * 负责配置Kafka Source和Sink
 */
public class KafkaSourceConfig {
    
    private static final Logger LOG = LoggerFactory.getLogger(KafkaSourceConfig.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 创建Kafka Source用于读取原始裁判文书数据
     */
    public static KafkaSource<JudgmentDocument> createKafkaSource(Properties properties) {
        String bootstrapServers = properties.getProperty("kafka.bootstrap.servers", "localhost:9092");
        String groupId = properties.getProperty("kafka.group.id", "judgment-document-clean-group");
        String inputTopic = properties.getProperty("kafka.topic.input", "judgment-documents-raw");
        String offsetReset = properties.getProperty("kafka.auto.offset.reset", "latest");
        
        OffsetsInitializer offsetsInitializer = OffsetResetStrategy.LATEST.name().equalsIgnoreCase(offsetReset) 
            ? OffsetsInitializer.latest() 
            : OffsetsInitializer.earliest();
        
        return KafkaSource.<JudgmentDocument>builder()
                .setBootstrapServers(bootstrapServers)
                .setTopics(inputTopic)
                .setGroupId(groupId)
                .setStartingOffsets(offsetsInitializer)
                .setValueOnlyDeserializer(new JudgmentDocumentDeserializationSchema())
                .build();
    }
    
    /**
     * 创建Kafka Sink用于输出清洗后的裁判文书数据
     */
    public static KafkaSink<JudgmentDocument> createKafkaSink(Properties properties) {
        String bootstrapServers = properties.getProperty("kafka.bootstrap.servers", "localhost:9092");
        String outputTopic = properties.getProperty("kafka.topic.output", "judgment-documents-cleaned");
        
        return KafkaSink.<JudgmentDocument>builder()
                .setBootstrapServers(bootstrapServers)
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        .setTopic(outputTopic)
                        .setValueSerializationSchema(new JudgmentDocumentSerializationSchema())
                        .build())
                .build();
    }
    
    /**
     * 裁判文书反序列化Schema
     */
    public static class JudgmentDocumentDeserializationSchema implements DeserializationSchema<JudgmentDocument> {
        
        @Override
        public JudgmentDocument deserialize(byte[] message) throws IOException {
            try {
                String jsonString = new String(message, StandardCharsets.UTF_8);
                LOG.debug("Deserializing message: {}", jsonString);
                return objectMapper.readValue(jsonString, JudgmentDocument.class);
            } catch (Exception e) {
                LOG.error("Failed to deserialize message: {}", new String(message, StandardCharsets.UTF_8), e);
                // 返回一个错误标记的文档，而不是抛出异常
                JudgmentDocument errorDoc = new JudgmentDocument();
                errorDoc.setId("ERROR_" + System.currentTimeMillis());
                errorDoc.setCleanErrors("Deserialization failed: " + e.getMessage());
                return errorDoc;
            }
        }
        
        @Override
        public boolean isEndOfStream(JudgmentDocument nextElement) {
            return false;
        }
        
        @Override
        public TypeInformation<JudgmentDocument> getProducedType() {
            return TypeInformation.of(JudgmentDocument.class);
        }
    }
    
    /**
     * 裁判文书序列化Schema
     */
    public static class JudgmentDocumentSerializationSchema implements SerializationSchema<JudgmentDocument> {
        
        @Override
        public byte[] serialize(JudgmentDocument element) {
            try {
                String jsonString = objectMapper.writeValueAsString(element);
                LOG.debug("Serializing document: {}", element.getId());
                return jsonString.getBytes(StandardCharsets.UTF_8);
            } catch (Exception e) {
                LOG.error("Failed to serialize document: {}", element.getId(), e);
                // 返回错误信息的JSON
                String errorJson = String.format(
                    "{\"id\":\"%s\",\"error\":\"Serialization failed: %s\"}", 
                    element.getId(), 
                    e.getMessage()
                );
                return errorJson.getBytes(StandardCharsets.UTF_8);
            }
        }
    }
}
