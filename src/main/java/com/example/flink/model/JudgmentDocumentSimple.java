package com.example.flink.model;

import java.io.Serializable;
import java.util.Objects;

/**
 * Judgment Document Data Model - Simplified Version (for syntax validation)
 * Used to represent judgment document data received and processed from Kafka
 */
public class JudgmentDocumentSimple implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private String id;
    private String caseNumber;
    private String courtName;
    private String caseType;
    private String judgmentDate;
    private String parties;
    private String content;
    private String judgeName;
    private String caseCategory;
    private String verdict;
    private Long timestamp;
    private String source;
    
    // Clean status flag
    private Boolean isCleaned = false;
    private String cleanErrors;
    
    public JudgmentDocumentSimple() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public JudgmentDocumentSimple(String id, String caseNumber, String courtName, String content) {
        this();
        this.id = id;
        this.caseNumber = caseNumber;
        this.courtName = courtName;
        this.content = content;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getCaseNumber() {
        return caseNumber;
    }
    
    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }
    
    public String getCourtName() {
        return courtName;
    }
    
    public void setCourtName(String courtName) {
        this.courtName = courtName;
    }
    
    public String getCaseType() {
        return caseType;
    }
    
    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }
    
    public String getJudgmentDate() {
        return judgmentDate;
    }
    
    public void setJudgmentDate(String judgmentDate) {
        this.judgmentDate = judgmentDate;
    }
    
    public String getParties() {
        return parties;
    }
    
    public void setParties(String parties) {
        this.parties = parties;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getJudgeName() {
        return judgeName;
    }
    
    public void setJudgeName(String judgeName) {
        this.judgeName = judgeName;
    }
    
    public String getCaseCategory() {
        return caseCategory;
    }
    
    public void setCaseCategory(String caseCategory) {
        this.caseCategory = caseCategory;
    }
    
    public String getVerdict() {
        return verdict;
    }
    
    public void setVerdict(String verdict) {
        this.verdict = verdict;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public Boolean getIsCleaned() {
        return isCleaned;
    }
    
    public void setIsCleaned(Boolean isCleaned) {
        this.isCleaned = isCleaned;
    }
    
    public String getCleanErrors() {
        return cleanErrors;
    }
    
    public void setCleanErrors(String cleanErrors) {
        this.cleanErrors = cleanErrors;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JudgmentDocumentSimple that = (JudgmentDocumentSimple) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "JudgmentDocument{" +
                "id='" + id + '\'' +
                ", caseNumber='" + caseNumber + '\'' +
                ", courtName='" + courtName + '\'' +
                ", caseType='" + caseType + '\'' +
                ", judgmentDate='" + judgmentDate + '\'' +
                ", isCleaned=" + isCleaned +
                ", timestamp=" + timestamp +
                '}';
    }
}
