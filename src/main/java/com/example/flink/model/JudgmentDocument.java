package com.example.flink.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Objects;

/**
 * 裁判文书数据模型
 * 用于表示从Kafka接收和处理的裁判文书数据
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class JudgmentDocument implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("case_number")
    private String caseNumber;
    
    @JsonProperty("court_name")
    private String courtName;
    
    @JsonProperty("case_type")
    private String caseType;
    
    @JsonProperty("judgment_date")
    private String judgmentDate;
    
    @JsonProperty("parties")
    private String parties;
    
    @JsonProperty("content")
    private String content;
    
    @JsonProperty("judge_name")
    private String judgeName;
    
    @JsonProperty("case_category")
    private String caseCategory;
    
    @JsonProperty("verdict")
    private String verdict;
    
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @JsonProperty("source")
    private String source;
    
    // Clean status flag
    @JsonProperty("is_cleaned")
    private Boolean isCleaned = false;

    @JsonProperty("clean_errors")
    private String cleanErrors;
    
    public JudgmentDocument() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public JudgmentDocument(String id, String caseNumber, String courtName, String content) {
        this();
        this.id = id;
        this.caseNumber = caseNumber;
        this.courtName = courtName;
        this.content = content;
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getCaseNumber() {
        return caseNumber;
    }
    
    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }
    
    public String getCourtName() {
        return courtName;
    }
    
    public void setCourtName(String courtName) {
        this.courtName = courtName;
    }
    
    public String getCaseType() {
        return caseType;
    }
    
    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }
    
    public String getJudgmentDate() {
        return judgmentDate;
    }
    
    public void setJudgmentDate(String judgmentDate) {
        this.judgmentDate = judgmentDate;
    }
    
    public String getParties() {
        return parties;
    }
    
    public void setParties(String parties) {
        this.parties = parties;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public String getJudgeName() {
        return judgeName;
    }
    
    public void setJudgeName(String judgeName) {
        this.judgeName = judgeName;
    }
    
    public String getCaseCategory() {
        return caseCategory;
    }
    
    public void setCaseCategory(String caseCategory) {
        this.caseCategory = caseCategory;
    }
    
    public String getVerdict() {
        return verdict;
    }
    
    public void setVerdict(String verdict) {
        this.verdict = verdict;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public Boolean getIsCleaned() {
        return isCleaned;
    }
    
    public void setIsCleaned(Boolean isCleaned) {
        this.isCleaned = isCleaned;
    }
    
    public String getCleanErrors() {
        return cleanErrors;
    }
    
    public void setCleanErrors(String cleanErrors) {
        this.cleanErrors = cleanErrors;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JudgmentDocument that = (JudgmentDocument) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "JudgmentDocument{" +
                "id='" + id + '\'' +
                ", caseNumber='" + caseNumber + '\'' +
                ", courtName='" + courtName + '\'' +
                ", caseType='" + caseType + '\'' +
                ", judgmentDate='" + judgmentDate + '\'' +
                ", isCleaned=" + isCleaned +
                ", timestamp=" + timestamp +
                '}';
    }
}
