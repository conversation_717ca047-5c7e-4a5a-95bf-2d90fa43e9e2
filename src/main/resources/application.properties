# Kafka Configuration
kafka.bootstrap.servers=localhost:9092
kafka.group.id=judgment-document-clean-group
kafka.topic.input=judgment-documents-raw
kafka.topic.output=judgment-documents-cleaned
kafka.auto.offset.reset=latest

# Flink Configuration
flink.parallelism=4
flink.checkpoint.interval=60000
flink.checkpoint.timeout=300000
flink.restart.strategy=fixed-delay
flink.restart.attempts=3
flink.restart.delay=10000

# Data Cleaning Configuration
clean.remove.html.tags=true
clean.normalize.whitespace=true
clean.remove.special.chars=true
clean.min.content.length=100
clean.max.content.length=50000

# Logging Configuration
logging.level.root=INFO
logging.level.com.example.flink=DEBUG
