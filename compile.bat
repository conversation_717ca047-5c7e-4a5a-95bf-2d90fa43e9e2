@echo off
echo Compiling Java source files...

REM Create output directory
if not exist "target\classes" mkdir target\classes

REM Compile the source files (without dependencies for basic syntax check)
javac -d target\classes -sourcepath src\main\java src\main\java\com\example\flink\model\*.java
if %errorlevel% neq 0 (
    echo Model compilation failed!
    exit /b 1
)

javac -d target\classes -sourcepath src\main\java -cp target\classes src\main\java\com\example\flink\config\*.java
if %errorlevel% neq 0 (
    echo Config compilation failed!
    exit /b 1
)

javac -d target\classes -sourcepath src\main\java -cp target\classes src\main\java\com\example\flink\step\*.java
if %errorlevel% neq 0 (
    echo Step compilation failed!
    exit /b 1
)

javac -d target\classes -sourcepath src\main\java -cp target\classes src\main\java\com\example\flink\job\*.java
if %errorlevel% neq 0 (
    echo Job compilation failed!
    exit /b 1
)

echo Compilation successful!
echo.
echo Project structure created successfully:
echo - Main job class: JudgmentDocumentCleanJob
echo - Data cleaning logic: CleanStep
echo - Data model: JudgmentDocument
echo - Kafka configuration: KafkaSourceConfig
echo.
echo To run this project, you need:
echo 1. Apache Flink cluster
echo 2. Apache Kafka cluster
echo 3. Maven for dependency management and packaging
echo.
echo Next steps:
echo 1. Install Maven: https://maven.apache.org/download.cgi
echo 2. Run: mvn clean package
echo 3. Submit to Flink: flink run target/flink-risk-demo-1.0-SNAPSHOT.jar
